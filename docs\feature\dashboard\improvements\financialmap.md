I need to refactor the networth data collection and calculation logic in the financialmap system. The networth consists of four components: strategicFundValue, investmentsValue, assetsValue, and totalValue.

**Current Implementation:**
- strategicFundValue: Uses complex calculation via "aggregateStrategicFundFromTransactions" function that considers transactions, but can be overridden if user sets a manual value
- investmentsValue and assetsValue: Can be manually added or removed by users
- A batch job scheduler runs at month-end to save networth snapshots to financial map history
- This scheduler approach is problematic: service failures make data recovery complex and create reliability issues

**Required Changes:**

1. **Remove the scheduler dependency** by implementing a transaction-based networth history system

2. **Create hidden transactions in financialsheet** for all three networth components (strategicFundValue, investmentsValue, assetsValue):
   - These hidden transactions will serve as the source of truth for networth history
   - This allows networth history calculation by querying user transactions instead of relying on scheduled snapshots

3. **Implement efficient networth history caching**:
   - Save calculated networth history to avoid recalculation on every financialmap query
   - Only update/recalculate when underlying data changes (transactions are added/modified)
   - Implement cache invalidation strategy when relevant transactions change

**Technical Requirements:**
- Preserve the existing "aggregateStrategicFundFromTransactions" function behavior
- Ensure hidden transactions don't interfere with user-visible financial data
- Implement efficient query patterns for networth history retrieval
- Handle edge cases where manual overrides exist for strategicFundValue

Please analyze the current financialmap implementation and propose a detailed technical approach for this refactoring.

## Implementation Plan (No Backward Compatibility Required)

Since this feature isn't in production yet, we can implement a clean solution without migration concerns.

### 1. Remove Scheduler System Completely
- Delete `internal/scheduler/` package entirely
- Remove scheduler initialization from `internal/api/server.go`
- Remove `CreateMonthlySnapshot` and `FindLatestNetWorthSnapshot` methods
- Remove `NetWorthHistory` field from `FinancialMap` model

### 2. Implement Clean Hidden Transaction System

**New Category Identifiers:**
```go
// Add to internal/model/financialsheet/category.go
CategoryIdentifierNetworthStrategicFund CategoryIdentifier = "networth_strategic_fund"
CategoryIdentifierNetworthInvestments   CategoryIdentifier = "networth_investments"
CategoryIdentifierNetworthAssets        CategoryIdentifier = "networth_assets"
```

**New Category Type:**
```go
CategoryTypeNetworthTracking CategoryType = "networth_tracking"
```

**Hidden Transaction Properties:**
- Special `MoneySource` and `PaymentMethod` for networth tracking
- `Hidden` field in Transaction model to exclude from user queries
- Date represents the month being tracked (first day of month)

### 3. Direct Transaction-Based Networth History

**Core Implementation:**
- Create hidden transactions when networth components change
- Query transactions directly for networth history (no caching initially)
- Use MongoDB aggregation for efficient history calculation

**Service Methods:**
```go
// Create hidden networth transaction
func (s *service) createNetworthTransaction(ctx context.Context, userID string, component NetworthComponent, value monetary.Amount, date time.Time) error

// Calculate networth history from hidden transactions
func (s *service) calculateNetworthHistory(ctx context.Context, userID string, months int) ([]*NetWorthSnapshot, error)
```

### 4. Updated FinancialMap Model
```go
type FinancialMap struct {
    // Remove NetWorthHistory field entirely
    ObjectID         primitive.ObjectID  `json:"-" bson:"_id,omitempty"`
    UserID           string              `json:"userID" bson:"userID"`
    MonthlyIncome    monetary.Amount     `json:"monthlyIncome" bson:"monthlyIncome"`
    StrategicFund    *StrategicFund      `json:"strategicFund" bson:"strategicFund"`
    TotalInvestments monetary.Amount     `json:"totalInvestments" bson:"totalInvestments"`
    TotalAssets      monetary.Amount     `json:"totalAssets" bson:"totalAssets"`
    // NetWorthHistory removed - calculated from transactions
    IncomeSources    []*IncomeSource     `json:"incomeSources" bson:"incomeSources"`
    Investments      []*Investment       `json:"investments" bson:"investments"`
    Assets           []*Asset            `json:"assets" bson:"assets"`
}
```

### 5. Modified Service Logic
- `FindNetWorthHistory`: Query hidden transactions directly
- `UpdateStrategicFund/CreateInvestment/etc.`: Create hidden transactions after updates
- Remove all dynamic snapshot creation logic from `updateFinancialMapWithLiveData`

### 6. Implementation Order

1. **Remove scheduler** (clean slate)
2. **Add hidden transaction categories**
3. **Implement hidden transaction creation**
4. **Update FinancialMap model** (remove NetWorthHistory)
5. **Implement transaction-based history calculation**
6. **Update all service methods** to create hidden transactions
7. **Add caching later** (optional optimization)

### Benefits of This Approach:
- ✅ **Remove all scheduler code immediately**
- ✅ **No migration complexity**
- ✅ **Clean model without legacy fields**
- ✅ **Direct implementation of transaction-based system**
- ✅ **Simpler testing and validation**

## Implementation Progress

### ✅ COMPLETED: Remove Scheduler Dependencies
**Status:** Complete
**Files Modified:**
- Removed `internal/scheduler/scheduler.go` entirely
- Removed scheduler import and initialization from `internal/api/server.go`
- Removed `CreateMonthlySnapshot` and `FindLatestNetWorthSnapshot` methods from:
  - `internal/service/dashboard/service.go` (interface)
  - `internal/service/dashboard/financialmap.go` (implementation)
  - `internal/repository/dashboard/repository.go` (interface)
  - `internal/repository/dashboard/financialmap.go` (implementation)
  - `internal/controller/dashboard/controller.go` (interface and routes)
  - `internal/controller/dashboard/financialmap.go` (implementation)
- Removed related test methods from:
  - `internal/service/dashboard/service_test.go`
  - `internal/controller/dashboard/controller_test.go`
- All tests passing, build successful

### ✅ COMPLETED: Create Hidden Transaction Categories
**Status:** Complete
**Files Modified:**
- `internal/model/financialsheet/category.go`:
  - Added new CategoryIdentifier constants: `CategoryIdentifierNetworthStrategicFund`, `CategoryIdentifierNetworthInvestments`, `CategoryIdentifierNetworthAssets`
  - Added new CategoryType: `CategoryTypeNetworthTracking`
  - Updated validation methods to include new categories
- `internal/model/financialsheet/money_source.go`:
  - Added `MoneySourceNetworthTracking` for hidden transactions
  - Updated validation method
- `internal/model/financialsheet/payment_method.go`:
  - Added `PaymentMethodNetworthTracking` for hidden transactions
  - Updated validation method
- `internal/model/financialsheet/record.go`:
  - Added `Hidden` field to Transaction model
- `internal/service/dashboard/networth_tracking.go`:
  - Created helper functions for creating hidden networth transactions
  - Implemented `createNetworthTransaction` method
  - Added NetworthComponent enum for different networth types

### ✅ COMPLETED: Implement Transaction-Based Networth Tracking
**Status:** Complete
**Files Modified:**
- `internal/service/dashboard/networth_tracking.go`:
  - Created `createNetworthTransaction` helper function
  - Added `NetworthComponent` enum for different networth types
  - Added `getMonthStart` utility function for consistent dating
- `internal/service/dashboard/financialmap.go`:
  - Updated `UpdateStrategicFund` to create hidden transactions
  - Updated `CreateInvestment`, `UpdateInvestment`, `DeleteInvestment` to create hidden transactions
  - Updated `CreateAsset`, `UpdateAsset`, `DeleteAsset` to create hidden transactions
  - All networth component changes now create hidden transactions for tracking

### ✅ COMPLETED: Update FinancialMap Service Logic
**Status:** Complete
**Files Modified:**
- `internal/service/dashboard/networth_tracking.go`:
  - Implemented `calculateNetworthHistory` method for transaction-based networth history calculation
  - Added proper month-based aggregation and snapshot generation
- `internal/service/dashboard/financialmap.go`:
  - Updated `FindNetWorthHistory` to use transaction-based calculation
  - Removed dynamic snapshot creation logic from `updateFinancialMapWithLiveData`
  - Removed NetWorthHistory field manipulation in update methods
- `internal/model/dashboard/financialmap.go`:
  - Removed `NetWorthHistory` field from FinancialMap model
  - Added comment explaining transaction-based approach
- `internal/repository/dashboard/repository.go`:
  - Removed `FindNetWorthHistory` method from repository interface
- `internal/repository/dashboard/financialmap.go`:
  - Removed `FindNetWorthHistory` method implementation
- `internal/controller/dashboard/financialmap.go`:
  - Updated controller to use service method instead of model field
  - Added `buildNetWorthHistoryCardFromService` helper method
- `internal/service/dashboard/service_test.go`:
  - Removed NetWorthHistory field references from test structs
  - Removed mock repository method for FindNetWorthHistory

### ✅ COMPLETED: Full Transaction-Based Networth System
**Status:** Complete
**Summary:**
- ✅ **Scheduler system completely removed**
- ✅ **Hidden transaction categories implemented**
- ✅ **Transaction-based networth tracking active**
- ✅ **NetWorthHistory field removed from model**
- ✅ **Service layer updated to use transaction-based calculation**
- ✅ **Repository layer cleaned up**
- ✅ **Controller updated to use service methods**
- ✅ **All tests updated and passing**
- ✅ **Build successful**