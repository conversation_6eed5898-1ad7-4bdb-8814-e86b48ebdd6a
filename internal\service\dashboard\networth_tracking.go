package dashboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NetworthComponent represents the different components of networth
type NetworthComponent string

const (
	NetworthComponentStrategicFund NetworthComponent = "strategic_fund"
	NetworthComponentInvestments   NetworthComponent = "investments"
	NetworthComponentAssets        NetworthComponent = "assets"
)

// createNetworthTransaction creates a hidden transaction for networth tracking
func (s *service) createNetworthTransaction(ctx context.Context, userID string, component NetworthComponent, value monetary.Amount, date time.Time) error {
	// Get the user's financial record
	record, err := s.FinancialSheetService.FindByUser(ctx, userID)
	if err != nil {
		return err
	}

	// Create hidden transaction based on component type
	var categoryIdentifier financialsheet.CategoryIdentifier
	switch component {
	case NetworthComponentStrategicFund:
		categoryIdentifier = financialsheet.CategoryIdentifierNetworthStrategicFund
	case NetworthComponentInvestments:
		categoryIdentifier = financialsheet.CategoryIdentifierNetworthInvestments
	case NetworthComponentAssets:
		categoryIdentifier = financialsheet.CategoryIdentifierNetworthAssets
	default:
		return errors.New(errors.Service, "invalid networth component", errors.Validation, nil)
	}

	// Create the hidden transaction
	transaction := &financialsheet.Transaction{
		ObjectID:      primitive.NewObjectID(),
		MoneySource:   financialsheet.MoneySourceNetworthTracking,
		PaymentMethod: financialsheet.PaymentMethodNetworthTracking,
		Icon:          financialsheet.CategoryIconUndefined, // Use undefined icon for hidden transactions
		Category:      categoryIdentifier,
		Value:         value,
		Date:          date,
		Type:          financialsheet.CategoryTypeNetworthTracking,
		Hidden:        true, // Mark as hidden
	}

	// Create the transaction using the financial sheet service
	_, err = s.FinancialSheetService.CreateTransaction(ctx, record, transaction, false)
	return err
}

// Helper function to get the first day of the month for consistent dating
func getMonthStart(date time.Time) time.Time {
	return time.Date(date.Year(), date.Month(), 1, 0, 0, 0, 0, time.UTC)
}
